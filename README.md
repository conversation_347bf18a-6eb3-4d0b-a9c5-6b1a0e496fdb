# Coach Me - Development Phases & Prompts (REVISED)

## Phase 1: Foundation & Core Features
**Focus:** Basic app structure, UI, and manual scheduling

### Prompt for Phase 1:
```
Create a React Native app called "Coach Me" with the following features:

1. **Project Setup:**
   - React Native CLI project (not Expo for better native module support)
   - Clean folder structure with components, screens, utils, services
   - Navigation setup with React Navigation
   - Basic theming and styling system

2. **Core UI Components:**
   - Welcome/onboarding screen
   - Topic selection screen (buttons for: Meditation, Gym Workout, Learning)
   - Manual scheduling interface (time picker, frequency selector)
   - Schedule list/overview screen
   - Basic settings screen

3. **Scheduling System:**
   - Manual time input with native time pickers
   - Frequency selection (daily, weekly, specific days)
   - Topic + time combination storage
   - Edit/delete scheduled activities

4. **Data Management:**
   - AsyncStorage for local data persistence
   - Data models for scheduled activities
   - CRUD operations for schedules

**Technical Requirements:**
- React Native CLI (not Expo)
- React Navigation 6+
- AsyncStorage for data persistence
- TypeScript (recommended)
- Clean component architecture

**Deliverables:**
- Working app with manual scheduling
- Clean UI/UX foundation
- Local data persistence
- Navigation between screens
- README with setup instructions

**Success Criteria:**
- User can create, view, edit, and delete scheduled coaching activities
- App works on both iOS and Android
- Clean, intuitive interface
```

---

## Phase 2: Scheduling Logic & Time Validation
**Focus:** Convert natural language time expressions into structured schedule data

### Prompt for Phase 2:
```
Extend the Coach Me app from Phase 1 with scheduling capabilities:

1. **Time Parsing & Validation:**
   - Convert natural language time expressions to structured data
   - Handle formats: "8 AM daily", "every Tuesday at 7 PM", "weekday mornings"
   - Validate time inputs and ask for clarification if unclear
   - Generate recurring schedule objects (daily, weekly, specific days)

2. **Schedule Confirmation Flow:**
   - After topic + time extraction, show a confirmation screen
   - Voice confirmation: "So you want to [topic] at [time]. Is that correct?"
   - If incorrect, restart the flow or allow editing

3. **Motivational Question Integration:**
   - After schedule confirmation, ask: "Why is this important to you?"
   - Store the user's motivation response
   - Connect motivation to the scheduled activity

4. **Data Structure:**
   - Design schema for storing scheduled activities
   - Include: topic, schedule pattern, motivation, created date
   - Use AsyncStorage for local data persistence

5. **Enhanced UI:**
   - Schedule preview screen
   - Edit/modify schedule interface
   - List of all scheduled activities

**Technical Requirements:**
- Create utility functions for time parsing
- Implement local data storage
- Add form validation and error handling
- Create reusable schedule components

**Deliverables:**
- Extended app with scheduling functionality
- Local data persistence
- Schedule management interface
```

---

## Phase 3: Push Notifications & Background Tasks
**Focus:** Implement notification system and background scheduling

### Prompt for Phase 3:
```
Add notification and background task capabilities to the Coach Me app:

1. **Push Notification Setup:**
   - Integrate React Native Push Notifications or Expo Notifications
   - Request notification permissions on app launch
   - Handle iOS and Android notification differences

2. **Notification Scheduling:**
   - Convert scheduled activities into notification triggers
   - Support different recurrence patterns (daily, weekly, custom)
   - Schedule notifications with proper timing

3. **Notification Content:**
   - Personalized messages: "Time for your meditation session!"
   - Include user's motivation in notification
   - Add action buttons: "Start", "Snooze (5 min)", "Skip"

4. **Background Task Management:**
   - Handle notification scheduling when app is closed
   - Reschedule notifications after device reboot
   - Clean up expired/old notifications

5. **Notification Response Handling:**
   - Detect when user taps notification
   - Navigate to appropriate coaching session
   - Handle snooze functionality

6. **Settings & Preferences:**
   - Allow users to enable/disable notifications per activity
   - Notification timing preferences
   - Snooze duration settings

**Technical Requirements:**
- Implement proper notification permissions flow
- Handle app state changes (foreground/background)
- Add notification scheduling logic
- Test on both iOS and Android devices

**Deliverables:**
- Working notification system
- Background task handling
- User preference settings
- Notification management interface
```

---

## Phase 4: Coaching Session Engine & Content Delivery
**Focus:** Create guided coaching experiences for different topics

### Prompt for Phase 4:
```
Build the coaching session engine for the Coach Me app:

1. **Session Structure:**
   - Create coaching session framework with start/pause/stop controls
   - Timer functionality for timed sessions
   - Progress tracking within sessions

2. **Topic-Specific Content:**
   - **Meditation:** Breathing exercises, guided relaxation, mindfulness prompts
   - **Gym Workout:** Exercise instructions, rest timers, motivation
   - **Learning (e.g., Angular):** Structured lessons, practice exercises, key concepts
   - Make content modular and easily extensible

3. **Voice-Guided Sessions:**
   - Text-to-Speech delivery of coaching content
   - Proper pacing and pauses in voice delivery
   - Interactive voice prompts ("Are you ready for the next exercise?")

4. **Session Customization:**
   - Duration settings (5min, 10min, 30min sessions)
   - Difficulty levels where applicable
   - Personal preferences (voice speed, background sounds)

5. **Progress Tracking:**
   - Session completion tracking
   - Streak counters
   - Basic statistics (total sessions, favorite topics)

6. **Session Interface:**
   - Clean, distraction-free session UI
   - Large, easy-to-tap controls
   - Progress indicators
   - Emergency exit/pause options

**Technical Requirements:**
- Create session management system
- Implement audio playback controls
- Add persistent progress tracking
- Design responsive session interface
- Handle interruptions (calls, other apps)

**Deliverables:**
- Complete coaching session engine
- Multiple topic templates
- Progress tracking system
- Session management interface
```

---

## Phase 5: Calendar Integration & External APIs
**Focus:** Connect with external calendar services and enhance scheduling

### Prompt for Phase 5:
```
Integrate external calendar services and enhance the Coach Me app:

1. **Calendar API Integration:**
   - Google Calendar API integration
   - Apple Calendar (EventKit) for iOS
   - OAuth authentication flow for Google Calendar
   - Permission handling for calendar access

2. **Calendar Event Management:**
   - Create recurring calendar events for scheduled coaching sessions
   - Sync app schedules with external calendars
   - Handle calendar event updates and deletions
   - Avoid duplicate events

3. **Calendar Conflict Detection:**
   - Check for existing calendar events at requested times
   - Suggest alternative times if conflicts exist
   - Show calendar availability when scheduling

4. **Enhanced Scheduling:**
   - Import existing calendar events to suggest optimal coaching times
   - Smart scheduling based on user's calendar patterns
   - Timezone handling for calendar integration

5. **Synchronization:**
   - Keep app data in sync with calendar changes
   - Handle offline scenarios
   - Conflict resolution when data differs

6. **User Experience:**
   - Calendar permission onboarding
   - Clear indication of calendar sync status
   - Option to disable calendar integration

**Technical Requirements:**
- Implement OAuth flows for Google Calendar
- Handle iOS EventKit permissions
- Add error handling for API failures
- Implement offline capability
- Add calendar sync status indicators

**Deliverables:**
- Full calendar integration
- OAuth authentication system
- Sync management interface
- Conflict resolution system
```

---

## Phase 6: Advanced LLM Integration & Personalization
**Focus:** Integrate AI for personalized coaching and smart responses

### Prompt for Phase 6:
```
Add advanced AI capabilities to personalize the Coach Me experience:

1. **LLM Integration:**
   - Integrate with OpenAI API, Google Gemini, or Claude API
   - Create conversation contexts for different coaching topics
   - Handle API rate limits and errors gracefully

2. **Personalized Coaching:**
   - Generate custom coaching content based on user's goals and progress
   - Adapt session difficulty based on user feedback and completion rates
   - Create personalized motivational messages

3. **Smart Conversation Flow:**
   - Natural language understanding for schedule modifications
   - Context-aware responses during coaching sessions
   - Intelligent follow-up questions based on user responses

4. **Progress Analysis:**
   - AI-powered insights on user's coaching journey
   - Suggestions for improvement and goal adjustment
   - Identify patterns in user behavior and preferences

5. **Dynamic Content Generation:**
   - Generate new meditation scripts based on user preferences
   - Create custom workout routines
   - Adapt learning content to user's pace and interests

6. **Voice Personality:**
   - Consistent AI coaching personality across interactions
   - Tone adaptation based on session type and user mood
   - Personalized encouragement and motivation

**Technical Requirements:**
- Implement secure API key management
- Add conversation context management
- Handle AI response streaming for better UX
- Implement fallback content for API failures
- Add usage monitoring and cost tracking

**Deliverables:**
- Full LLM integration
- Personalized content generation
- Smart conversation system
- Progress analysis features
```

---

## Implementation Order:
1. **Phase 1** - Get basic voice interaction working
2. **Phase 2** - Add scheduling and data persistence
3. **Phase 3** - Implement notifications and background tasks
4. **Phase 4** - Build coaching session experiences
5. **Phase 5** - Add calendar integration
6. **Phase 6** - Enhance with AI personalization

## Notes for Each Phase:
- Each phase builds upon the previous one
- Test thoroughly before moving to the next phase
- Keep the app functional at the end of each phase
- Focus on core functionality before adding advanced features
- Consider user feedback between phases for improvements