import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types';
import { StorageService } from '../services/StorageService';
import CustomButton from '../components/CustomButton';

type SettingsScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Settings'
>;

interface SettingsScreenProps {
  navigation: SettingsScreenNavigationProp;
}

interface SettingItemProps {
  title: string;
  subtitle?: string;
  onPress?: () => void;
  rightComponent?: React.ReactNode;
  showArrow?: boolean;
}

const SettingItem: React.FC<SettingItemProps> = ({
  title,
  subtitle,
  onPress,
  rightComponent,
  showArrow = false,
}) => {
  return (
    <TouchableOpacity
      style={styles.settingItem}
      onPress={onPress}
      disabled={!onPress}
      activeOpacity={onPress ? 0.7 : 1}
    >
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      
      <View style={styles.settingRight}>
        {rightComponent}
        {showArrow && <Text style={styles.arrow}>›</Text>}
      </View>
    </TouchableOpacity>
  );
};

const SettingsScreen: React.FC<SettingsScreenProps> = ({ navigation }) => {
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(true);

  const handleClearAllData = () => {
    Alert.alert(
      'Clear All Data',
      'This will delete all your scheduled activities and progress. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: async () => {
            try {
              await StorageService.clearAllData();
              Alert.alert('Success', 'All data has been cleared.');
              navigation.navigate('Welcome');
            } catch (error) {
              Alert.alert('Error', 'Failed to clear data.');
            }
          },
        },
      ]
    );
  };

  const handleAbout = () => {
    Alert.alert(
      'About Coach Me',
      'Coach Me v1.0.0\n\nYour personal coaching companion for building better habits.\n\nDeveloped with React Native and Expo.',
      [{ text: 'OK' }]
    );
  };

  const handleFeedback = () => {
    Alert.alert(
      'Send Feedback',
      'We\'d love to hear from you! Please send your feedback to:\n\<EMAIL>',
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          
          <SettingItem
            title="Enable Notifications"
            subtitle="Get reminders for your scheduled sessions"
            rightComponent={
              <Switch
                value={notificationsEnabled}
                onValueChange={setNotificationsEnabled}
                trackColor={{ false: '#E0E0E0', true: '#6B73FF' }}
                thumbColor={notificationsEnabled ? '#fff' : '#f4f3f4'}
              />
            }
          />
          
          <SettingItem
            title="Sound"
            subtitle="Play sounds with notifications"
            rightComponent={
              <Switch
                value={soundEnabled}
                onValueChange={setSoundEnabled}
                trackColor={{ false: '#E0E0E0', true: '#6B73FF' }}
                thumbColor={soundEnabled ? '#fff' : '#f4f3f4'}
                disabled={!notificationsEnabled}
              />
            }
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Schedule</Text>
          
          <SettingItem
            title="View All Activities"
            subtitle="Manage your coaching schedule"
            onPress={() => navigation.navigate('ScheduleList')}
            showArrow
          />
          
          <SettingItem
            title="Add New Activity"
            subtitle="Create a new coaching session"
            onPress={() => navigation.navigate('TopicSelection')}
            showArrow
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data</Text>
          
          <SettingItem
            title="Clear All Data"
            subtitle="Delete all activities and progress"
            onPress={handleClearAllData}
            rightComponent={<Text style={styles.dangerText}>Clear</Text>}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          
          <SettingItem
            title="Send Feedback"
            subtitle="Help us improve Coach Me"
            onPress={handleFeedback}
            showArrow
          />
          
          <SettingItem
            title="About"
            subtitle="App version and information"
            onPress={handleAbout}
            showArrow
          />
        </View>

        <View style={styles.footer}>
          <CustomButton
            title="Back to Home"
            onPress={() => navigation.navigate('Welcome')}
            variant="outline"
            size="medium"
            style={styles.backButton}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  
  scrollView: {
    flex: 1,
  },
  
  section: {
    backgroundColor: '#fff',
    marginTop: 16,
  },
  
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  
  settingContent: {
    flex: 1,
  },
  
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  
  settingSubtitle: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  
  settingRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  arrow: {
    fontSize: 20,
    color: '#999',
    marginLeft: 8,
  },
  
  dangerText: {
    fontSize: 16,
    color: '#FF6B6B',
    fontWeight: '500',
  },
  
  footer: {
    padding: 24,
    marginTop: 32,
  },
  
  backButton: {
    width: '100%',
  },
});

export default SettingsScreen;
