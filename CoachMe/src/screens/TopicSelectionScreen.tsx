import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList, CoachingTopic } from '../types';
import { COACHING_TOPICS } from '../utils/coachingTopics';
import TopicCard from '../components/TopicCard';
import CustomButton from '../components/CustomButton';

type TopicSelectionScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'TopicSelection'
>;

interface TopicSelectionScreenProps {
  navigation: TopicSelectionScreenNavigationProp;
}

const TopicSelectionScreen: React.FC<TopicSelectionScreenProps> = ({ navigation }) => {
  const [selectedTopic, setSelectedTopic] = useState<CoachingTopic | null>(null);

  const handleTopicSelect = (topic: CoachingTopic) => {
    setSelectedTopic(topic);
  };

  const handleContinue = () => {
    if (!selectedTopic) {
      Alert.alert('Please select a topic', 'Choose what you\'d like to focus on.');
      return;
    }

    navigation.navigate('Scheduling', { topic: selectedTopic });
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>What would you like to focus on?</Text>
        <Text style={styles.subtitle}>
          Choose a topic to start building your coaching schedule
        </Text>
      </View>

      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {COACHING_TOPICS.map((topic) => (
          <TopicCard
            key={topic.id}
            topic={topic}
            onPress={handleTopicSelect}
            selected={selectedTopic?.id === topic.id}
          />
        ))}
      </ScrollView>

      <View style={styles.footer}>
        <CustomButton
          title="Continue"
          onPress={handleContinue}
          variant="primary"
          size="large"
          disabled={!selectedTopic}
          style={styles.continueButton}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  
  header: {
    paddingHorizontal: 24,
    paddingVertical: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  
  scrollView: {
    flex: 1,
  },
  
  scrollContent: {
    paddingVertical: 16,
    paddingBottom: 100, // Extra space for footer
  },
  
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  
  continueButton: {
    width: '100%',
  },
});

export default TopicSelectionScreen;
