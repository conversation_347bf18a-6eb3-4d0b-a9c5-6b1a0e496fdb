import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useFocusEffect } from '@react-navigation/native';
import { RootStackParamList, ScheduledActivity } from '../types';
import { StorageService } from '../services/StorageService';
import { formatSchedulePattern, getNextScheduledTime } from '../utils/timeUtils';
import CustomButton from '../components/CustomButton';

type ScheduleListScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'ScheduleList'
>;

interface ScheduleListScreenProps {
  navigation: ScheduleListScreenNavigationProp;
}

interface ScheduleItemProps {
  activity: ScheduledActivity;
  onToggleActive: (id: string, isActive: boolean) => void;
  onDelete: (id: string) => void;
}

const ScheduleItem: React.FC<ScheduleItemProps> = ({ activity, onToggleActive, onDelete }) => {
  const nextTime = getNextScheduledTime(activity.schedulePattern);
  
  const handleDelete = () => {
    Alert.alert(
      'Delete Schedule',
      `Are you sure you want to delete "${activity.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => onDelete(activity.id) },
      ]
    );
  };

  return (
    <View style={[styles.scheduleItem, !activity.isActive && styles.inactiveItem]}>
      <View style={styles.itemHeader}>
        <View style={styles.topicInfo}>
          <Text style={styles.topicIcon}>{activity.topic.icon}</Text>
          <View style={styles.topicDetails}>
            <Text style={[styles.itemTitle, !activity.isActive && styles.inactiveText]}>
              {activity.title}
            </Text>
            <Text style={[styles.scheduleText, !activity.isActive && styles.inactiveText]}>
              {formatSchedulePattern(activity.schedulePattern)}
            </Text>
          </View>
        </View>
        
        <TouchableOpacity
          style={[styles.toggleButton, activity.isActive ? styles.activeButton : styles.inactiveButton]}
          onPress={() => onToggleActive(activity.id, !activity.isActive)}
        >
          <Text style={[styles.toggleText, activity.isActive ? styles.activeToggleText : styles.inactiveToggleText]}>
            {activity.isActive ? 'Active' : 'Paused'}
          </Text>
        </TouchableOpacity>
      </View>

      {activity.motivation && (
        <Text style={[styles.motivation, !activity.isActive && styles.inactiveText]}>
          💭 {activity.motivation}
        </Text>
      )}

      {nextTime && activity.isActive && (
        <Text style={styles.nextSession}>
          Next session: {nextTime.toLocaleDateString()} at {nextTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </Text>
      )}

      <View style={styles.itemActions}>
        <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
          <Text style={styles.deleteButtonText}>Delete</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const ScheduleListScreen: React.FC<ScheduleListScreenProps> = ({ navigation }) => {
  const [activities, setActivities] = useState<ScheduledActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadActivities = async () => {
    try {
      const loadedActivities = await StorageService.getScheduledActivities();
      setActivities(loadedActivities);
    } catch (error) {
      console.error('Error loading activities:', error);
      Alert.alert('Error', 'Failed to load your schedule.');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadActivities();
    setRefreshing(false);
  };

  const handleToggleActive = async (id: string, isActive: boolean) => {
    try {
      await StorageService.updateActivityStatus(id, isActive);
      setActivities(prev => 
        prev.map(activity => 
          activity.id === id ? { ...activity, isActive } : activity
        )
      );
    } catch (error) {
      console.error('Error updating activity status:', error);
      Alert.alert('Error', 'Failed to update activity status.');
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await StorageService.deleteScheduledActivity(id);
      setActivities(prev => prev.filter(activity => activity.id !== id));
    } catch (error) {
      console.error('Error deleting activity:', error);
      Alert.alert('Error', 'Failed to delete activity.');
    }
  };

  const handleAddNew = () => {
    navigation.navigate('TopicSelection');
  };

  useFocusEffect(
    useCallback(() => {
      loadActivities();
    }, [])
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyIcon}>📅</Text>
      <Text style={styles.emptyTitle}>No scheduled activities</Text>
      <Text style={styles.emptySubtitle}>
        Start building your coaching routine by adding your first activity
      </Text>
      <CustomButton
        title="Add Your First Activity"
        onPress={handleAddNew}
        variant="primary"
        size="medium"
        style={styles.emptyButton}
      />
    </View>
  );

  const renderItem = ({ item }: { item: ScheduledActivity }) => (
    <ScheduleItem
      activity={item}
      onToggleActive={handleToggleActive}
      onDelete={handleDelete}
    />
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading your schedule...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Your Coaching Schedule</Text>
        <Text style={styles.headerSubtitle}>
          {activities.filter(a => a.isActive).length} active activities
        </Text>
      </View>

      <FlatList
        data={activities}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={activities.length === 0 ? styles.emptyContainer : styles.listContainer}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />

      {activities.length > 0 && (
        <View style={styles.footer}>
          <CustomButton
            title="Add New Activity"
            onPress={handleAddNew}
            variant="primary"
            size="medium"
            style={styles.addButton}
          />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  
  header: {
    backgroundColor: '#fff',
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  
  headerSubtitle: {
    fontSize: 16,
    color: '#666',
  },
  
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  
  listContainer: {
    paddingVertical: 16,
  },
  
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  
  emptyState: {
    alignItems: 'center',
  },
  
  emptyIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  
  emptySubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  
  emptyButton: {
    paddingHorizontal: 32,
  },
  
  scheduleItem: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  
  inactiveItem: {
    opacity: 0.6,
  },
  
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  
  topicInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  
  topicIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  
  topicDetails: {
    flex: 1,
  },
  
  itemTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  
  scheduleText: {
    fontSize: 14,
    color: '#666',
  },
  
  inactiveText: {
    color: '#999',
  },
  
  toggleButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
  },
  
  activeButton: {
    backgroundColor: '#4ECDC4',
    borderColor: '#4ECDC4',
  },
  
  inactiveButton: {
    backgroundColor: 'transparent',
    borderColor: '#999',
  },
  
  toggleText: {
    fontSize: 12,
    fontWeight: '500',
  },
  
  activeToggleText: {
    color: '#fff',
  },
  
  inactiveToggleText: {
    color: '#999',
  },
  
  motivation: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    marginBottom: 8,
    lineHeight: 18,
  },
  
  nextSession: {
    fontSize: 12,
    color: '#6B73FF',
    fontWeight: '500',
    marginBottom: 8,
  },
  
  itemActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  
  deleteButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  
  deleteButtonText: {
    fontSize: 14,
    color: '#FF6B6B',
    fontWeight: '500',
  },
  
  footer: {
    backgroundColor: '#fff',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  
  addButton: {
    width: '100%',
  },
});

export default ScheduleListScreen;
