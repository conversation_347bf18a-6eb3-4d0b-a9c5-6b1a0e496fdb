import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types';
import CustomButton from '../components/CustomButton';

type WelcomeScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Welcome'
>;

interface WelcomeScreenProps {
  navigation: WelcomeScreenNavigationProp;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ navigation }) => {
  const handleGetStarted = () => {
    navigation.navigate('TopicSelection');
  };

  const handleViewSchedule = () => {
    navigation.navigate('ScheduleList');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#6B73FF" />
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.emoji}>🎯</Text>
          <Text style={styles.title}>Coach Me</Text>
          <Text style={styles.subtitle}>
            Your personal coaching companion for building better habits
          </Text>
        </View>

        <View style={styles.features}>
          <View style={styles.feature}>
            <Text style={styles.featureIcon}>🧘</Text>
            <Text style={styles.featureText}>Meditation & Mindfulness</Text>
          </View>
          
          <View style={styles.feature}>
            <Text style={styles.featureIcon}>💪</Text>
            <Text style={styles.featureText}>Fitness & Workouts</Text>
          </View>
          
          <View style={styles.feature}>
            <Text style={styles.featureIcon}>📚</Text>
            <Text style={styles.featureText}>Learning & Skills</Text>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <CustomButton
            title="Get Started"
            onPress={handleGetStarted}
            variant="primary"
            size="large"
            style={styles.primaryButton}
          />
          
          <CustomButton
            title="View My Schedule"
            onPress={handleViewSchedule}
            variant="outline"
            size="medium"
            style={styles.secondaryButton}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#6B73FF',
  },
  
  content: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 40,
  },
  
  header: {
    alignItems: 'center',
    marginTop: 60,
  },
  
  emoji: {
    fontSize: 80,
    marginBottom: 20,
  },
  
  title: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 16,
  },
  
  subtitle: {
    fontSize: 18,
    color: '#fff',
    textAlign: 'center',
    opacity: 0.9,
    lineHeight: 24,
  },
  
  features: {
    alignItems: 'center',
  },
  
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    width: '100%',
  },
  
  featureIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  
  featureText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '500',
  },
  
  buttonContainer: {
    gap: 16,
  },
  
  primaryButton: {
    backgroundColor: '#fff',
  },
  
  secondaryButton: {
    borderColor: '#fff',
  },
});

export default WelcomeScreen;
