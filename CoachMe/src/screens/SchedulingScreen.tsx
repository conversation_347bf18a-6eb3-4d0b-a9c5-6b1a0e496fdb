import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList, ScheduledActivity, SchedulePattern } from '../types';
import { StorageService } from '../services/StorageService';
import { formatTime12Hour, getCurrentTime, DAYS_OF_WEEK } from '../utils/timeUtils';
import CustomButton from '../components/CustomButton';

type SchedulingScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Scheduling'
>;

type SchedulingScreenRouteProp = RouteProp<RootStackParamList, 'Scheduling'>;

interface SchedulingScreenProps {
  navigation: SchedulingScreenNavigationProp;
  route: SchedulingScreenRouteProp;
}

const SchedulingScreen: React.FC<SchedulingScreenProps> = ({ navigation, route }) => {
  const { topic } = route.params;
  
  const [time, setTime] = useState(getCurrentTime());
  const [scheduleType, setScheduleType] = useState<'daily' | 'weekly' | 'custom'>('daily');
  const [selectedDays, setSelectedDays] = useState<number[]>([]);
  const [motivation, setMotivation] = useState('');
  const [loading, setLoading] = useState(false);

  const handleDayToggle = (dayIndex: number) => {
    setSelectedDays(prev => {
      if (prev.includes(dayIndex)) {
        return prev.filter(d => d !== dayIndex);
      } else {
        return [...prev, dayIndex].sort();
      }
    });
  };

  const handleTimeChange = (newTime: string) => {
    // Simple time validation
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (timeRegex.test(newTime) || newTime === '') {
      setTime(newTime);
    }
  };

  const validateForm = (): boolean => {
    if (!time || !/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time)) {
      Alert.alert('Invalid Time', 'Please enter a valid time in HH:MM format.');
      return false;
    }

    if ((scheduleType === 'weekly' || scheduleType === 'custom') && selectedDays.length === 0) {
      Alert.alert('Select Days', 'Please select at least one day for your schedule.');
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const schedulePattern: SchedulePattern = {
        type: scheduleType,
        time,
        days: scheduleType === 'daily' ? undefined : selectedDays,
      };

      const activity: ScheduledActivity = {
        id: StorageService.generateId(),
        topic,
        title: `${topic.name} Session`,
        schedulePattern,
        motivation: motivation.trim() || undefined,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await StorageService.saveScheduledActivity(activity);
      
      Alert.alert(
        'Schedule Created!',
        `Your ${topic.name} session has been scheduled successfully.`,
        [
          {
            text: 'View Schedule',
            onPress: () => navigation.navigate('ScheduleList'),
          },
          {
            text: 'Add Another',
            onPress: () => navigation.navigate('TopicSelection'),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to save your schedule. Please try again.');
      console.error('Error saving schedule:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.topicHeader}>
          <Text style={styles.topicIcon}>{topic.icon}</Text>
          <Text style={styles.topicName}>{topic.name}</Text>
          <Text style={styles.topicDescription}>{topic.description}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>When would you like to practice?</Text>
          
          <View style={styles.timeContainer}>
            <Text style={styles.label}>Time</Text>
            <TextInput
              style={styles.timeInput}
              value={time}
              onChangeText={handleTimeChange}
              placeholder="HH:MM"
              keyboardType="numeric"
              maxLength={5}
            />
            <Text style={styles.timeDisplay}>
              {time && formatTime12Hour(time)}
            </Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>How often?</Text>
          
          <View style={styles.scheduleTypeContainer}>
            {(['daily', 'weekly', 'custom'] as const).map((type) => (
              <TouchableOpacity
                key={type}
                style={[
                  styles.scheduleTypeButton,
                  scheduleType === type && styles.scheduleTypeButtonActive,
                ]}
                onPress={() => setScheduleType(type)}
              >
                <Text
                  style={[
                    styles.scheduleTypeText,
                    scheduleType === type && styles.scheduleTypeTextActive,
                  ]}
                >
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {(scheduleType === 'weekly' || scheduleType === 'custom') && (
            <View style={styles.daysContainer}>
              <Text style={styles.label}>Select Days</Text>
              <View style={styles.daysGrid}>
                {DAYS_OF_WEEK.map((day, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.dayButton,
                      selectedDays.includes(index) && styles.dayButtonActive,
                    ]}
                    onPress={() => handleDayToggle(index)}
                  >
                    <Text
                      style={[
                        styles.dayButtonText,
                        selectedDays.includes(index) && styles.dayButtonTextActive,
                      ]}
                    >
                      {day.slice(0, 3)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Why is this important to you?</Text>
          <Text style={styles.motivationSubtitle}>
            This will help motivate you during your sessions
          </Text>
          <TextInput
            style={styles.motivationInput}
            value={motivation}
            onChangeText={setMotivation}
            placeholder="e.g., I want to reduce stress and improve focus..."
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <CustomButton
          title="Create Schedule"
          onPress={handleSave}
          variant="primary"
          size="large"
          loading={loading}
          style={styles.saveButton}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  
  scrollView: {
    flex: 1,
  },
  
  topicHeader: {
    backgroundColor: '#fff',
    padding: 24,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  
  topicIcon: {
    fontSize: 48,
    marginBottom: 12,
  },
  
  topicName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  
  topicDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  
  section: {
    backgroundColor: '#fff',
    marginTop: 16,
    padding: 24,
  },
  
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  
  timeContainer: {
    alignItems: 'center',
  },
  
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  
  timeInput: {
    borderWidth: 2,
    borderColor: '#6B73FF',
    borderRadius: 8,
    padding: 12,
    fontSize: 18,
    textAlign: 'center',
    width: 100,
    marginBottom: 8,
  },
  
  timeDisplay: {
    fontSize: 16,
    color: '#666',
  },
  
  scheduleTypeContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  
  scheduleTypeButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    alignItems: 'center',
  },
  
  scheduleTypeButtonActive: {
    borderColor: '#6B73FF',
    backgroundColor: '#6B73FF',
  },
  
  scheduleTypeText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
  },
  
  scheduleTypeTextActive: {
    color: '#fff',
  },
  
  daysContainer: {
    marginTop: 20,
  },
  
  daysGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  
  dayButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    minWidth: 45,
    alignItems: 'center',
  },
  
  dayButtonActive: {
    borderColor: '#6B73FF',
    backgroundColor: '#6B73FF',
  },
  
  dayButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  
  dayButtonTextActive: {
    color: '#fff',
  },
  
  motivationSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  
  motivationInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 80,
  },
  
  footer: {
    backgroundColor: '#fff',
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  
  saveButton: {
    width: '100%',
  },
});

export default SchedulingScreen;
