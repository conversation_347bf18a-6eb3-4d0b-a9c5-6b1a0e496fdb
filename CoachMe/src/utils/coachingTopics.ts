import { CoachingTopic } from '../types';

export const COACHING_TOPICS: CoachingTopic[] = [
  {
    id: 'meditation',
    name: 'Meditation',
    description: 'Mindfulness and meditation practices for mental clarity and peace',
    icon: '🧘',
    color: '#6B73FF',
    defaultDuration: 10,
  },
  {
    id: 'gym_workout',
    name: 'Gym Workout',
    description: 'Physical fitness and strength training exercises',
    icon: '💪',
    color: '#FF6B6B',
    defaultDuration: 45,
  },
  {
    id: 'learning_angular',
    name: 'Learning Angular',
    description: 'Web development with Angular framework',
    icon: '📚',
    color: '#4ECDC4',
    defaultDuration: 30,
  },
  {
    id: 'learning_react',
    name: 'Learning React',
    description: 'Frontend development with React library',
    icon: '⚛️',
    color: '#45B7D1',
    defaultDuration: 30,
  },
  {
    id: 'reading',
    name: 'Reading',
    description: 'Daily reading habit for knowledge and relaxation',
    icon: '📖',
    color: '#96CEB4',
    defaultDuration: 20,
  },
  {
    id: 'writing',
    name: 'Writing',
    description: 'Creative writing and journaling practice',
    icon: '✍️',
    color: '#FFEAA7',
    defaultDuration: 15,
  },
  {
    id: 'language_learning',
    name: 'Language Learning',
    description: 'Practice and improve foreign language skills',
    icon: '🌍',
    color: '#DDA0DD',
    defaultDuration: 25,
  },
  {
    id: 'music_practice',
    name: 'Music Practice',
    description: 'Musical instrument practice and music theory',
    icon: '🎵',
    color: '#FFB347',
    defaultDuration: 30,
  },
];

export const getTopicById = (id: string): CoachingTopic | undefined => {
  return COACHING_TOPICS.find(topic => topic.id === id);
};

export const getTopicByName = (name: string): CoachingTopic | undefined => {
  return COACHING_TOPICS.find(topic => 
    topic.name.toLowerCase().includes(name.toLowerCase())
  );
};
