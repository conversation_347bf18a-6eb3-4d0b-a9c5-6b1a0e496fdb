import { SchedulePattern } from '../types';

export const DAYS_OF_WEEK = [
  'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
];

export const DAYS_SHORT = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

// Format time from 24-hour to 12-hour format
export const formatTime12Hour = (time24: string): string => {
  const [hours, minutes] = time24.split(':').map(Number);
  const period = hours >= 12 ? 'PM' : 'AM';
  const hours12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
  return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
};

// Format time from 12-hour to 24-hour format
export const formatTime24Hour = (time12: string): string => {
  const [time, period] = time12.split(' ');
  const [hours, minutes] = time.split(':').map(Number);
  
  let hours24 = hours;
  if (period === 'PM' && hours !== 12) {
    hours24 += 12;
  } else if (period === 'AM' && hours === 12) {
    hours24 = 0;
  }
  
  return `${hours24.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
};

// Get current time in HH:MM format
export const getCurrentTime = (): string => {
  const now = new Date();
  return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
};

// Format schedule pattern for display
export const formatSchedulePattern = (pattern: SchedulePattern): string => {
  switch (pattern.type) {
    case 'daily':
      return `Daily at ${formatTime12Hour(pattern.time)}`;
    
    case 'weekly':
      if (pattern.days && pattern.days.length > 0) {
        const dayNames = pattern.days
          .sort()
          .map(day => DAYS_SHORT[day])
          .join(', ');
        return `${dayNames} at ${formatTime12Hour(pattern.time)}`;
      }
      return `Weekly at ${formatTime12Hour(pattern.time)}`;
    
    case 'custom':
      if (pattern.days && pattern.days.length > 0) {
        const dayNames = pattern.days
          .sort()
          .map(day => DAYS_SHORT[day])
          .join(', ');
        return `${dayNames} at ${formatTime12Hour(pattern.time)}`;
      }
      return `Custom schedule at ${formatTime12Hour(pattern.time)}`;
    
    default:
      return `At ${formatTime12Hour(pattern.time)}`;
  }
};

// Check if a schedule is active today
export const isScheduleActiveToday = (pattern: SchedulePattern): boolean => {
  const today = new Date().getDay(); // 0 = Sunday, 6 = Saturday
  
  switch (pattern.type) {
    case 'daily':
      return true;
    
    case 'weekly':
    case 'custom':
      return pattern.days ? pattern.days.includes(today) : false;
    
    default:
      return false;
  }
};

// Get next scheduled time for an activity
export const getNextScheduledTime = (pattern: SchedulePattern): Date | null => {
  const now = new Date();
  const [hours, minutes] = pattern.time.split(':').map(Number);
  
  switch (pattern.type) {
    case 'daily': {
      const nextTime = new Date();
      nextTime.setHours(hours, minutes, 0, 0);
      
      // If time has passed today, schedule for tomorrow
      if (nextTime <= now) {
        nextTime.setDate(nextTime.getDate() + 1);
      }
      
      return nextTime;
    }
    
    case 'weekly':
    case 'custom': {
      if (!pattern.days || pattern.days.length === 0) return null;
      
      const today = now.getDay();
      const sortedDays = [...pattern.days].sort();
      
      // Find next day in the schedule
      let nextDay = sortedDays.find(day => {
        if (day === today) {
          // Check if time hasn't passed today
          const todayTime = new Date();
          todayTime.setHours(hours, minutes, 0, 0);
          return todayTime > now;
        }
        return day > today;
      });
      
      // If no day found this week, get first day of next week
      if (nextDay === undefined) {
        nextDay = sortedDays[0];
      }
      
      const nextTime = new Date();
      const daysToAdd = nextDay === sortedDays[0] && nextDay <= today ? 
        7 - today + nextDay : nextDay - today;
      
      nextTime.setDate(nextTime.getDate() + daysToAdd);
      nextTime.setHours(hours, minutes, 0, 0);
      
      return nextTime;
    }
    
    default:
      return null;
  }
};

// Validate time string format (HH:MM)
export const isValidTimeFormat = (time: string): boolean => {
  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
};
