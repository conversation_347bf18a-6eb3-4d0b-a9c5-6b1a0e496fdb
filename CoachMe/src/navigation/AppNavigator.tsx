import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { RootStackParamList } from '../types';

// Import screens (we'll create these next)
import WelcomeScreen from '../screens/WelcomeScreen';
import TopicSelectionScreen from '../screens/TopicSelectionScreen';
import SchedulingScreen from '../screens/SchedulingScreen';
import ScheduleListScreen from '../screens/ScheduleListScreen';
import SettingsScreen from '../screens/SettingsScreen';

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Welcome"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#6B73FF',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen
          name="Welcome"
          component={WelcomeScreen}
          options={{
            title: 'Coach Me',
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="TopicSelection"
          component={TopicSelectionScreen}
          options={{
            title: 'Choose Your Focus',
          }}
        />
        <Stack.Screen
          name="Scheduling"
          component={SchedulingScreen}
          options={{
            title: 'Schedule Your Session',
          }}
        />
        <Stack.Screen
          name="ScheduleList"
          component={ScheduleListScreen}
          options={{
            title: 'Your Schedule',
          }}
        />
        <Stack.Screen
          name="Settings"
          component={SettingsScreen}
          options={{
            title: 'Settings',
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
