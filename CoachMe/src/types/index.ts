// Core data types for Coach Me app

export interface ScheduledActivity {
  id: string;
  topic: CoachingTopic;
  title: string;
  schedulePattern: SchedulePattern;
  motivation?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface SchedulePattern {
  type: 'daily' | 'weekly' | 'custom';
  time: string; // HH:MM format
  days?: number[]; // 0-6 (Sunday-Saturday) for weekly/custom
  frequency?: number; // for custom patterns
}

export interface CoachingTopic {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  defaultDuration: number; // in minutes
}

export interface SessionProgress {
  activityId: string;
  completedSessions: number;
  totalSessions: number;
  currentStreak: number;
  longestStreak: number;
  lastSessionDate?: Date;
}

// Navigation types
export type RootStackParamList = {
  Welcome: undefined;
  TopicSelection: undefined;
  Scheduling: {
    topic: CoachingTopic;
  };
  ScheduleList: undefined;
  Settings: undefined;
};

// Form types
export interface ScheduleFormData {
  topic: CoachingTopic;
  time: string;
  scheduleType: 'daily' | 'weekly' | 'custom';
  selectedDays: number[];
  motivation: string;
}

// Storage keys
export const STORAGE_KEYS = {
  SCHEDULED_ACTIVITIES: 'scheduled_activities',
  SESSION_PROGRESS: 'session_progress',
  USER_PREFERENCES: 'user_preferences',
} as const;
