import AsyncStorage from '@react-native-async-storage/async-storage';
import { ScheduledActivity, SessionProgress, STORAGE_KEYS } from '../types';

export class StorageService {
  // Scheduled Activities CRUD operations
  static async getScheduledActivities(): Promise<ScheduledActivity[]> {
    try {
      const data = await AsyncStorage.getItem(STORAGE_KEYS.SCHEDULED_ACTIVITIES);
      if (!data) return [];
      
      const activities = JSON.parse(data);
      // Convert date strings back to Date objects
      return activities.map((activity: any) => ({
        ...activity,
        createdAt: new Date(activity.createdAt),
        updatedAt: new Date(activity.updatedAt),
      }));
    } catch (error) {
      console.error('Error loading scheduled activities:', error);
      return [];
    }
  }

  static async saveScheduledActivity(activity: ScheduledActivity): Promise<void> {
    try {
      const activities = await this.getScheduledActivities();
      const existingIndex = activities.findIndex(a => a.id === activity.id);
      
      if (existingIndex >= 0) {
        activities[existingIndex] = { ...activity, updatedAt: new Date() };
      } else {
        activities.push(activity);
      }
      
      await AsyncStorage.setItem(
        STORAGE_KEYS.SCHEDULED_ACTIVITIES,
        JSON.stringify(activities)
      );
    } catch (error) {
      console.error('Error saving scheduled activity:', error);
      throw error;
    }
  }

  static async deleteScheduledActivity(activityId: string): Promise<void> {
    try {
      const activities = await this.getScheduledActivities();
      const filteredActivities = activities.filter(a => a.id !== activityId);
      
      await AsyncStorage.setItem(
        STORAGE_KEYS.SCHEDULED_ACTIVITIES,
        JSON.stringify(filteredActivities)
      );
    } catch (error) {
      console.error('Error deleting scheduled activity:', error);
      throw error;
    }
  }

  static async updateActivityStatus(activityId: string, isActive: boolean): Promise<void> {
    try {
      const activities = await this.getScheduledActivities();
      const activity = activities.find(a => a.id === activityId);
      
      if (activity) {
        activity.isActive = isActive;
        activity.updatedAt = new Date();
        await this.saveScheduledActivity(activity);
      }
    } catch (error) {
      console.error('Error updating activity status:', error);
      throw error;
    }
  }

  // Session Progress operations
  static async getSessionProgress(activityId: string): Promise<SessionProgress | null> {
    try {
      const data = await AsyncStorage.getItem(STORAGE_KEYS.SESSION_PROGRESS);
      if (!data) return null;
      
      const allProgress = JSON.parse(data);
      const progress = allProgress[activityId];
      
      if (!progress) return null;
      
      return {
        ...progress,
        lastSessionDate: progress.lastSessionDate ? new Date(progress.lastSessionDate) : undefined,
      };
    } catch (error) {
      console.error('Error loading session progress:', error);
      return null;
    }
  }

  static async updateSessionProgress(progress: SessionProgress): Promise<void> {
    try {
      const data = await AsyncStorage.getItem(STORAGE_KEYS.SESSION_PROGRESS);
      const allProgress = data ? JSON.parse(data) : {};
      
      allProgress[progress.activityId] = progress;
      
      await AsyncStorage.setItem(
        STORAGE_KEYS.SESSION_PROGRESS,
        JSON.stringify(allProgress)
      );
    } catch (error) {
      console.error('Error updating session progress:', error);
      throw error;
    }
  }

  // Utility methods
  static async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.SCHEDULED_ACTIVITIES,
        STORAGE_KEYS.SESSION_PROGRESS,
        STORAGE_KEYS.USER_PREFERENCES,
      ]);
    } catch (error) {
      console.error('Error clearing all data:', error);
      throw error;
    }
  }

  static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}
