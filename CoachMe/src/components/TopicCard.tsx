import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  View,
  ViewStyle,
} from 'react-native';
import { CoachingTopic } from '../types';

interface TopicCardProps {
  topic: CoachingTopic;
  onPress: (topic: CoachingTopic) => void;
  selected?: boolean;
  style?: ViewStyle;
}

const TopicCard: React.FC<TopicCardProps> = ({
  topic,
  onPress,
  selected = false,
  style,
}) => {
  const cardStyle = [
    styles.card,
    { borderColor: topic.color },
    selected && { backgroundColor: topic.color, borderWidth: 3 },
    style,
  ];

  const textStyle = [
    styles.title,
    selected && styles.selectedTitle,
  ];

  const descriptionStyle = [
    styles.description,
    selected && styles.selectedDescription,
  ];

  return (
    <TouchableOpacity
      style={cardStyle}
      onPress={() => onPress(topic)}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        <Text style={styles.icon}>{topic.icon}</Text>
      </View>
      
      <View style={styles.content}>
        <Text style={textStyle}>{topic.name}</Text>
        <Text style={descriptionStyle}>{topic.description}</Text>
        
        <View style={styles.footer}>
          <Text style={[styles.duration, selected && styles.selectedDuration]}>
            {topic.defaultDuration} min
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    padding: 16,
    marginVertical: 8,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  
  iconContainer: {
    alignItems: 'center',
    marginBottom: 12,
  },
  
  icon: {
    fontSize: 48,
  },
  
  content: {
    flex: 1,
  },
  
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  
  selectedTitle: {
    color: '#fff',
  },
  
  description: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 12,
  },
  
  selectedDescription: {
    color: '#fff',
    opacity: 0.9,
  },
  
  footer: {
    alignItems: 'center',
  },
  
  duration: {
    fontSize: 12,
    color: '#999',
    fontWeight: '500',
  },
  
  selectedDuration: {
    color: '#fff',
    opacity: 0.8,
  },
});

export default TopicCard;
