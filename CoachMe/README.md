# Coach Me - Phase 1 Implementation

A React Native app for personal coaching and habit building.

## Phase 1 Features ✅

### Core Functionality
- **Welcome Screen**: Onboarding and navigation to main features
- **Topic Selection**: Choose from 8 predefined coaching topics (Meditation, Gym Workout, Learning, etc.)
- **Manual Scheduling**: Create schedules with time picker and frequency selection
- **Schedule Management**: View, edit, and delete scheduled activities
- **Local Data Persistence**: All data stored locally using AsyncStorage

### Technical Implementation
- **React Native with Expo**: Cross-platform mobile development
- **TypeScript**: Type-safe development
- **React Navigation**: Stack navigation between screens
- **AsyncStorage**: Local data persistence
- **Clean Architecture**: Organized folder structure with services, components, and utilities

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── CustomButton.tsx
│   └── TopicCard.tsx
├── navigation/          # Navigation configuration
│   └── AppNavigator.tsx
├── screens/            # Screen components
│   ├── WelcomeScreen.tsx
│   ├── TopicSelectionScreen.tsx
│   ├── SchedulingScreen.tsx
│   ├── ScheduleListScreen.tsx
│   └── SettingsScreen.tsx
├── services/           # Data services
│   └── StorageService.ts
├── types/              # TypeScript type definitions
│   └── index.ts
└── utils/              # Utility functions
    ├── coachingTopics.ts
    └── timeUtils.ts
```

## Getting Started

### Prerequisites
- Node.js (v16 or later)
- npm or yarn
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio/Emulator (for Android development)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd coach-me/CoachMe
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Run on device/simulator**
   ```bash
   # iOS
   npm run ios
   
   # Android
   npm run android
   
   # Web (for testing)
   npm run web
   ```

## Features Overview

### 🎯 Topic Selection
- 8 predefined coaching topics with icons and descriptions
- Visual topic cards with selection feedback
- Easy navigation to scheduling

### ⏰ Smart Scheduling
- Manual time input with validation
- Multiple frequency options (Daily, Weekly, Custom)
- Day selection for weekly/custom schedules
- Motivational question integration

### 📅 Schedule Management
- View all scheduled activities
- Toggle active/inactive status
- Delete activities with confirmation
- Next session time calculation
- Refresh functionality

### ⚙️ Settings
- Notification preferences (ready for Phase 5)
- Data management (clear all data)
- App information and feedback

## Data Models

### ScheduledActivity
```typescript
interface ScheduledActivity {
  id: string;
  topic: CoachingTopic;
  title: string;
  schedulePattern: SchedulePattern;
  motivation?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

### SchedulePattern
```typescript
interface SchedulePattern {
  type: 'daily' | 'weekly' | 'custom';
  time: string; // HH:MM format
  days?: number[]; // 0-6 (Sunday-Saturday)
  frequency?: number;
}
```

## Key Features Implemented

✅ **Complete UI/UX Flow**: Welcome → Topic Selection → Scheduling → Schedule List  
✅ **Data Persistence**: Local storage with AsyncStorage  
✅ **Schedule Management**: CRUD operations for activities  
✅ **Time Handling**: Robust time parsing and formatting  
✅ **Responsive Design**: Clean, mobile-first interface  
✅ **Error Handling**: User-friendly error messages and validation  
✅ **TypeScript**: Full type safety throughout the app  

## Next Steps (Phase 2)

The foundation is now ready for Phase 2 implementation:
- Voice input integration with @react-native-voice/voice
- Speech-to-text for topic selection
- Voice activity indicators
- Fallback mechanisms for voice failures

## Testing

The app has been tested for:
- ✅ TypeScript compilation
- ✅ Navigation flow
- ✅ Data persistence
- ✅ Schedule creation and management
- ✅ Time validation and formatting

## Contributing

This is Phase 1 of a 6-phase development plan. Each phase builds upon the previous one while maintaining a working application state.

## License

MIT License - see LICENSE file for details.
