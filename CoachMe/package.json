{"name": "coachme", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.4.0", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.0", "expo": "~53.0.12", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.11.1", "react-native-vector-icons": "^10.2.0", "react-dom": "19.0.0", "react-native-web": "^0.20.0", "@expo/metro-runtime": "~5.0.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}